# DAEIRA Local Database Setup Guide

## Overview
This guide will help you set up a local MySQL database for DAEIRA to store conversations, memory, and system data.

## Option 1: MySQL Server (Recommended)

### Step 1: Install MySQL Server

#### Windows:
1. **Download MySQL Installer**:
   - Go to: https://dev.mysql.com/downloads/mysql/
   - Download "MySQL Installer for Windows"

2. **Install MySQL**:
   - Run the installer
   - Choose "Developer Default" setup type
   - Set a strong root password (remember this!)
   - Complete the installation
   - MySQL will start automatically

#### Alternative: Using Chocolatey
```powershell
# Install Chocolatey if not already installed
# Then install MySQL
choco install mysql
```

### Step 2: Configure Environment Variables

1. **Copy the example environment file**:
   ```bash
   copy .env.example .env
   ```

2. **Edit `.env` file** with your settings:
   ```env
   # Database Configuration
   MYSQL_HOST=localhost
   MYSQL_PORT=3306
   MYSQL_USER=daeira
   MYSQL_PASSWORD=your_secure_password_here
   MYSQL_DATABASE=daeira

   # Email Configuration - DreamHost Settings
   IMAP_SERVER=imap.dreamhost.com
   IMAP_PORT=993
   IMAP_USERNAME=<EMAIL>
   IMAP_PASSWORD=lifecycleevent02025!?
   SMTP_SERVER=smtp.dreamhost.com
   SMTP_PORT=465

   # Model and Storage Paths
   MODEL_PATH=C:/DAEIRA/deepseek_R1_7B
   STUDY_DIRECTORY=C:/DAEIRA/STUDY
   CHROMADB_PATH=C:/DAEIRA/MEMORY
   ```

### Step 3: Run Database Setup Script

```bash
cd APP
python setup_database.py
```

The script will:
- ✅ Connect to MySQL as root
- ✅ Create the `daeira` database
- ✅ Create the `daeira` user with proper permissions
- ✅ Create required tables (`input_events`, `conversations`)
- ✅ Test the connection

### Step 4: Verify Setup

```bash
# Test that all imports work
python test_imports.py

# Start DAEIRA
python daeira.py
```

## Option 2: Docker MySQL (Alternative)

If you prefer Docker:

### Step 1: Install Docker Desktop
- Download from: https://www.docker.com/products/docker-desktop

### Step 2: Run MySQL Container
```bash
docker run --name daeira-mysql \
  -e MYSQL_ROOT_PASSWORD=root_password \
  -e MYSQL_DATABASE=daeira \
  -e MYSQL_USER=daeira \
  -e MYSQL_PASSWORD=daeira_password \
  -p 3306:3306 \
  -d mysql:8.0
```

### Step 3: Update .env file
```env
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=daeira
MYSQL_PASSWORD=daeira_password
MYSQL_DATABASE=daeira
```

### Step 4: Create Tables
```bash
python setup_database.py
```

## Option 3: XAMPP (Easiest for Beginners)

### Step 1: Install XAMPP
- Download from: https://www.apachefriends.org/
- Install and start MySQL service

### Step 2: Access phpMyAdmin
- Open browser: http://localhost/phpmyadmin
- Create database named `daeira`
- Create user `daeira` with password

### Step 3: Configure DAEIRA
- Update `.env` file with your credentials
- Run `python setup_database.py`

## Troubleshooting

### Common Issues:

#### 1. "Access denied for user 'root'"
**Solution**: Check your root password
```bash
# Reset MySQL root password if needed
mysql -u root -p
```

#### 2. "Can't connect to MySQL server"
**Solutions**:
- Ensure MySQL service is running
- Check Windows Services for "MySQL80" service
- Restart MySQL service if needed

#### 3. "ModuleNotFoundError: No module named 'mysql'"
**Solution**: Install MySQL connector
```bash
pip install mysql-connector-python
```

#### 4. "Access denied for user 'daeira'"
**Solution**: Re-run the setup script to recreate the user

### Verify MySQL Installation:
```bash
# Check if MySQL is running
mysql -u root -p -e "SELECT VERSION();"

# Check if daeira database exists
mysql -u root -p -e "SHOW DATABASES;"

# Check if daeira user exists
mysql -u root -p -e "SELECT User, Host FROM mysql.user WHERE User='daeira';"
```

## Database Schema

### Tables Created:

#### 1. `input_events`
Stores all input events (chat, email, documents):
- `event_id` (VARCHAR) - Unique identifier
- `timestamp` (DATETIME) - When event occurred
- `source_type` (VARCHAR) - Type of input (chat, email, etc.)
- `source_details` (JSON) - Additional metadata
- `content` (TEXT) - The actual content
- `priority` (INT) - Processing priority
- `initial_tags` (JSON) - Classification tags

#### 2. `conversations`
Stores chat conversations:
- `id` (INT) - Auto-increment primary key
- `session_id` (VARCHAR) - Chat session identifier
- `user_message` (TEXT) - User's message
- `assistant_message` (TEXT) - DAEIRA's response
- `timestamp` (DATETIME) - When conversation occurred

## Security Considerations

### 1. Strong Passwords
- Use strong passwords for MySQL root and daeira user
- Never commit passwords to version control

### 2. Network Security
- MySQL is configured for localhost only by default
- For production, consider additional security measures

### 3. Backup Strategy
```bash
# Create backup
mysqldump -u daeira -p daeira > daeira_backup.sql

# Restore backup
mysql -u daeira -p daeira < daeira_backup.sql
```

## Performance Optimization

### 1. MySQL Configuration
Add to MySQL config file (`my.cnf` or `my.ini`):
```ini
[mysqld]
innodb_buffer_pool_size = 256M
max_connections = 100
query_cache_size = 64M
```

### 2. Index Optimization
The setup script creates indexes on frequently queried columns:
- `timestamp` for chronological queries
- `source_type` for filtering by input type
- `session_id` for conversation retrieval

## Next Steps

After successful database setup:

1. **Test DAEIRA**: Start the application and test basic functionality
2. **Configure Email**: Update email credentials in `.env`
3. **Set Up TTS**: Configure text-to-speech settings
4. **Load Model**: Ensure your AI model is in the correct path
5. **Test Memory**: Upload a document to test memory storage

## Support

If you encounter issues:
1. Check the logs in `daeira_email.log`
2. Verify all environment variables are set correctly
3. Test database connection manually
4. Ensure all required Python packages are installed

The database setup is now complete and DAEIRA should be able to store and retrieve data properly!
