import threading
import logging
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ip<PERSON>
from datetime import datetime
import asyncio
import re
from typing import Optional, Dict, Any

from input import start_email_polling, fetch_and_process_emails, _load_config, InputObject
from evaluate import evaluate_input

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('daeira_email.log')
    ]
)
logger = logging.getLogger(__name__)

class EmailResponseGenerator:
    """Handles email response generation using DAEIRA's 3-inference system"""
    
    def __init__(self, model_instance, tokenizer_instance, memory_manager):
        self.model_instance = model_instance
        self.tokenizer_instance = tokenizer_instance
        self.memory_manager = memory_manager
        self.config = _load_config()
    
    async def should_respond_to_email(self, input_obj) -> Dict[str, Any]:
        """Determine if DAEIRA should respond to this email and how"""
        
        # Build evaluation prompt for email triage
        email_triage_prompt = f"""You are <PERSON><PERSON>'s email triage system. Analyze this incoming email and determine the appropriate response action.

Email Details:
From: {input_obj.source_details.get('from_address', 'Unknown')}
Subject: {input_obj.source_details.get('subject', 'No Subject')}
Content: {input_obj.content}

Determine:
1. Should you respond to this email? (YES/NO)
2. What type of response is needed?
3. What tone should be used?
4. Any special considerations?

Categories to consider:
- Business inquiries (YES - professional response)
- Creative collaboration requests (YES - enthusiastic response)
- Spam/marketing (NO - ignore)
- Personal messages to Victor (NO - forward notification only)
- Technical/support requests (YES - helpful response)
- Appointment/scheduling (YES - coordinate response)

Respond in this format:
RESPOND: YES/NO
TYPE: [business/creative/technical/scheduling/personal]
TONE: [professional/friendly/enthusiastic/helpful]
PRIORITY: [high/normal/low]
REASONING: [brief explanation]"""

        # Use the current inference system (cycle 3 cognitive processing)
        try:
            # Create an InputObject for the email triage
            triage_input = InputObject(
                source_type='email_triage',
                source_details={'from_address': input_obj.source_details.get('from_address', 'Unknown')},
                content_type='text/plain',
                content=email_triage_prompt
            )

            # Use the cognitive cycle 3 processing
            response = await evaluate_input(
                input_obj=triage_input,
                model_instance=self.model_instance,
                tokenizer_instance=self.tokenizer_instance,
                memory_manager=self.memory_manager
            )
            
            # Parse the response
            decision = self._parse_triage_response(response)
            decision['thought_process'] = response  # The full response contains the thinking
            
            logger.info(f"Email triage decision: {decision}")
            return decision
            
        except Exception as e:
            logger.error(f"Error in email triage: {e}")
            # Default to safe response
            return {
                'respond': False,
                'type': 'unknown',
                'tone': 'professional',
                'priority': 'low',
                'reasoning': f"Error in analysis: {e}"
            }
    
    def _parse_triage_response(self, response: str) -> Dict[str, Any]:
        """Parse the triage response into structured data"""
        decision = {
            'respond': False,
            'type': 'unknown',
            'tone': 'professional', 
            'priority': 'normal',
            'reasoning': 'Unable to parse response'
        }
        
        try:
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('RESPOND:'):
                    decision['respond'] = 'YES' in line.upper()
                elif line.startswith('TYPE:'):
                    decision['type'] = line.split(':', 1)[1].strip().lower()
                elif line.startswith('TONE:'):
                    decision['tone'] = line.split(':', 1)[1].strip().lower()
                elif line.startswith('PRIORITY:'):
                    decision['priority'] = line.split(':', 1)[1].strip().lower()
                elif line.startswith('REASONING:'):
                    decision['reasoning'] = line.split(':', 1)[1].strip()
        except Exception as e:
            logger.error(f"Error parsing triage response: {e}")
        
        return decision
    
    async def generate_email_response(self, input_obj, triage_decision) -> str:
        """Generate email response using DAEIRA's full cognitive system"""
        
        # Build context-aware email response prompt
        email_response_prompt = f"""You are DAEIRA, AI assistant for Victor Wondu and the DUAL TRACE creative group. You've received an email that requires a response.

Email Context:
From: {input_obj.source_details.get('from_address', 'Unknown')}
Subject: {input_obj.source_details.get('subject', 'No Subject')}
Received: {input_obj.timestamp.strftime('%Y-%m-%d %H:%M')}

Original Email:
{input_obj.content}

Response Guidelines:
- Type: {triage_decision['type']}
- Tone: {triage_decision['tone']}
- Priority: {triage_decision['priority']}
- Reasoning: {triage_decision['reasoning']}

Your identity:
- You are DAEIRA, AI assistant and agent for Victor Wondu
- Victor operates DUAL TRACE creative group (artist and designer)
- You can speak for the studio on routine matters
- You should CC Victor on important items or defer to him when appropriate

Draft a professional email response. Include:
1. Appropriate greeting
2. Acknowledgment of their email
3. Helpful response to their inquiry/request
4. Next steps or call to action if needed
5. Professional signature as DAEIRA for DUAL TRACE

Keep the response concise but complete. Match their level of formality."""

        try:
            # Create an InputObject for the email response generation
            response_input = InputObject(
                source_type='email_response',
                source_details={'from_address': input_obj.source_details.get('from_address', 'Unknown')},
                content_type='text/plain',
                content=email_response_prompt
            )

            # Use the cognitive cycle 3 processing
            response = await evaluate_input(
                input_obj=response_input,
                model_instance=self.model_instance,
                tokenizer_instance=self.tokenizer_instance,
                memory_manager=self.memory_manager
            )
            
            logger.info(f"Generated email response: {len(response)} characters")
            return response
            
        except Exception as e:
            logger.error(f"Error generating email response: {e}")
            # Fallback response
            return f"""Thank you for your email. I'm DAEIRA, AI assistant for DUAL TRACE creative group.

I've received your message and will make sure it gets the attention it deserves. Victor Wondu will be in touch with you shortly.

Best regards,
DAEIRA
AI Assistant, DUAL TRACE Creative Group

---
This is an automated response. For urgent matters, please contact Victor directly."""
    
    async def send_email_response(self, to_address: str, subject: str, body: str) -> bool:
        """Send email response via SMTP"""
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.config['IMAP_USERNAME']
            msg['To'] = to_address
            msg['Subject'] = subject
            
            # Add body
            msg.attach(MIMEText(body, 'plain'))
            
            # Connect to SMTP server
            # Using DreamHost SMTP settings
            smtp_server = self.config.get('SMTP_SERVER', 'smtp.dreamhost.com')
            smtp_port = self.config.get('SMTP_PORT', 465)

            # Use SMTP_SSL for port 465, SMTP with STARTTLS for port 587
            if smtp_port == 465:
                server = smtplib.SMTP_SSL(smtp_server, smtp_port)
            else:
                server = smtplib.SMTP(smtp_server, smtp_port)
                server.starttls()  # Enable encryption

            server.login(self.config['IMAP_USERNAME'], self.config['IMAP_PASSWORD'])
            
            # Send email
            text = msg.as_string()
            server.sendmail(self.config['IMAP_USERNAME'], to_address, text)
            server.quit()
            
            logger.info(f"✓ Email sent successfully to {to_address}")
            logger.info(f"  Subject: {subject}")
            logger.info(f"  Body length: {len(body)} characters")
            
            return True
            
        except Exception as e:
            logger.error(f"✗ Failed to send email to {to_address}: {e}")
            return False

async def process_email_with_daeira_intelligence(input_obj, model_instance, tokenizer_instance, memory_manager):
    """Enhanced email processing with full DAEIRA cognitive capabilities"""
    
    logger.info(f"=== PROCESSING EMAIL: {input_obj.event_id} ===")
    logger.info(f"From: {input_obj.source_details.get('from_address', 'Unknown')}")
    logger.info(f"Subject: {input_obj.source_details.get('subject', 'No Subject')}")
    
    try:
        # Record the email in memory
        if memory_manager:
            event_id = memory_manager.record_input_event(input_obj)
            logger.info(f"Email recorded in memory: {event_id}")
        
        # Initialize response generator
        response_generator = EmailResponseGenerator(model_instance, tokenizer_instance, memory_manager)
        
        # Step 1: Evaluate if we should respond
        triage_decision = await response_generator.should_respond_to_email(input_obj)
        
        if not triage_decision['respond']:
            logger.info(f"✓ Email triaged - No response needed: {triage_decision['reasoning']}")
            
            # Still log it for Victor's awareness
            if memory_manager:
                memory_manager.structured_store.update_input_object_post_evaluation(
                    input_obj.event_id,
                    f"Email received - No response required: {triage_decision['reasoning']}",
                    []
                )
            return
        
        logger.info(f"✓ Email requires response: {triage_decision['type']} ({triage_decision['tone']} tone)")
        
        # Step 2: Generate response
        response_text = await response_generator.generate_email_response(input_obj, triage_decision)
        
        # Step 3: Send response
        original_subject = input_obj.source_details.get('subject', 'Your Email')
        response_subject = f"Re: {original_subject}" if not original_subject.startswith('Re:') else original_subject
        
        send_success = await response_generator.send_email_response(
            to_address=input_obj.source_details['from_address'],
            subject=response_subject,
            body=response_text
        )
        
        # Step 4: Update memory with results
        if memory_manager:
            status = "sent" if send_success else "failed"
            memory_manager.structured_store.update_input_object_post_evaluation(
                input_obj.event_id,
                f"Email processed - Response {status}. Type: {triage_decision['type']}, Priority: {triage_decision['priority']}",
                []
            )
        
        if send_success:
            logger.info(f"✓ Email fully processed and responded to: {input_obj.event_id}")
        else:
            logger.error(f"✗ Email processed but response failed: {input_obj.event_id}")
            
    except Exception as e:
        logger.error(f"✗ Error processing email {input_obj.event_id}: {str(e)}", exc_info=True)
        
        # Update memory with error
        if memory_manager:
            memory_manager.structured_store.update_input_object_post_evaluation(
                input_obj.event_id,
                f"Email processing error: {str(e)}",
                []
            )

async def process_input(input_obj, model_instance=None, tokenizer_instance=None, memory_manager=None):
    """Enhanced input processing dispatcher"""
    try:
        if input_obj.source_type == 'email':
            await process_email_with_daeira_intelligence(
                input_obj, model_instance, tokenizer_instance, memory_manager
            )
        else:
            # Handle other input types with current evaluation system
            evaluation_result = await evaluate_input(
                input_obj,
                model_instance,
                tokenizer_instance,
                memory_manager
            )
            
            logger.info(f"Input {input_obj.event_id} evaluated: {evaluation_result}")
            
            if memory_manager:
                memory_manager.structured_store.update_input_object_post_evaluation(
                    input_obj.event_id,
                    f"Non-email input processed: {evaluation_result}",
                    []
                )
        
    except Exception as e:
        logger.error(f"Error processing input {input_obj.event_id}: {str(e)}", exc_info=True)

def start_email_service(model_instance=None, tokenizer_instance=None, memory_manager=None):
    """Start the enhanced email service with full DAEIRA intelligence"""
    logger.info("=== INITIALIZING DAEIRA EMAIL INTELLIGENCE SYSTEM ===")

    # Create a dispatcher function that captures the model, tokenizer, and memory manager
    async def enhanced_dispatch_handler(input_obj):
        await process_input(input_obj, model_instance, tokenizer_instance, memory_manager)

    # Start email polling with the enhanced dispatch handler
    email_thread = threading.Thread(
        target=start_email_polling,
        args=(enhanced_dispatch_handler,),
        daemon=True
    )
    email_thread.start()

    # Provide a function to manually trigger email checking
    def manual_email_check():
        logger.info("Manual email check triggered")
        fetch_and_process_emails(enhanced_dispatch_handler)

    logger.info("✓ DAEIRA Email Intelligence System activated")
    logger.info("✓ Monitoring: <EMAIL>")
    logger.info("✓ Capabilities: Receive, analyze, and respond intelligently")
    logger.info("✓ Integration: Full 3-inference cognitive processing")

    return manual_email_check